#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Directories to exclude from analysis
const EXCLUDED_DIRS = [
  'node_modules',
  '.git',
  '.next',
  'dist',
  'build',
  'coverage',
  '.nyc_output',
  'out'
];

// File extensions to analyze
const CODE_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// Files that are typically entry points and should not be considered unused
const ENTRY_POINT_PATTERNS = [
  /^index\.(ts|tsx|js|jsx)$/,
  /^app\.(ts|tsx|js|jsx)$/,
  /^main\.(ts|tsx|js|jsx)$/,
  /^server\.(ts|tsx|js|jsx)$/,
  /\.config\.(ts|js)$/,
  /\.test\.(ts|tsx|js|jsx)$/,
  /\.spec\.(ts|tsx|js|jsx)$/,
  /^setup/,
  /^jest/,
  /^vitest/,
  /^cypress/,
  /^playwright/,
  /middleware\.(ts|js)$/,
  /layout\.(tsx|jsx)$/,
  /page\.(tsx|jsx)$/,
  /route\.(ts|js)$/,
  /api\//,
  /pages\//,
  /app\//
];

// Additional patterns for files that should be preserved even if not directly imported
const PRESERVE_PATTERNS = [
  // Next.js specific files
  /sitemap\.(ts|js)$/,
  /robots\.(ts|js)$/,
  /metadata\.(ts|js)$/,
  /loading\.(tsx|jsx)$/,
  /error\.(tsx|jsx)$/,
  /not-found\.(tsx|jsx)$/,
  /global-error\.(tsx|jsx)$/,
  /template\.(tsx|jsx)$/,

  // Configuration and setup files
  /\.d\.ts$/,
  /next-env\.d\.ts$/,
  /tailwind\.config/,
  /jest\.setup/,

  // Mock files for testing
  /__mocks__\//,
  /\.mock\.(ts|js)$/,

  // Utility and library files that might be used dynamically
  /utils\//,
  /lib\//,
  /types\//,
  /schemas\//,
  /config\//,

  // Component libraries (UI components)
  /components\/ui\//,

  // Scripts and tools
  /scripts\//,

  // Files with specific purposes
  /actions\.(ts|js)$/,
  /hooks\//,
  /context\//,

  // Files that might be imported dynamically or via string paths
  /\.client\.(ts|tsx|js|jsx)$/,
  /\.server\.(ts|js)$/
];

function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!EXCLUDED_DIRS.includes(file)) {
        getAllFiles(filePath, fileList);
      }
    } else {
      const ext = path.extname(file);
      if (CODE_EXTENSIONS.includes(ext)) {
        fileList.push(filePath);
      }
    }
  });
  
  return fileList;
}

function extractImports(content, filePath) {
  const imports = new Set();
  
  // Regular expressions to match different import patterns
  const importPatterns = [
    // ES6 imports: import ... from '...'
    /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+)?['"`]([^'"`]+)['"`]/g,
    // Dynamic imports: import('...')
    /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
    // CommonJS require: require('...')
    /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
    // Next.js dynamic imports
    /dynamic\s*\(\s*\(\s*\)\s*=>\s*import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g,
  ];
  
  importPatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const importPath = match[1];
      
      // Skip external packages (those that don't start with . or /)
      if (!importPath.startsWith('.') && !importPath.startsWith('/')) {
        continue;
      }
      
      // Resolve relative imports
      let resolvedPath = importPath;
      if (importPath.startsWith('.')) {
        resolvedPath = path.resolve(path.dirname(filePath), importPath);
      }
      
      // Try different extensions if no extension is provided
      if (!path.extname(resolvedPath)) {
        for (const ext of CODE_EXTENSIONS) {
          const withExt = resolvedPath + ext;
          if (fs.existsSync(withExt)) {
            imports.add(withExt);
            break;
          }
        }
        // Also try index files
        for (const ext of CODE_EXTENSIONS) {
          const indexFile = path.join(resolvedPath, `index${ext}`);
          if (fs.existsSync(indexFile)) {
            imports.add(indexFile);
            break;
          }
        }
      } else {
        if (fs.existsSync(resolvedPath)) {
          imports.add(resolvedPath);
        }
      }
    }
  });
  
  return imports;
}

function isEntryPoint(filePath) {
  const fileName = path.basename(filePath);
  const relativePath = path.relative(process.cwd(), filePath);

  return ENTRY_POINT_PATTERNS.some(pattern => {
    if (pattern instanceof RegExp) {
      return pattern.test(fileName) || pattern.test(relativePath);
    }
    return fileName.includes(pattern) || relativePath.includes(pattern);
  });
}

function shouldPreserveFile(filePath) {
  const fileName = path.basename(filePath);
  const relativePath = path.relative(process.cwd(), filePath);

  return PRESERVE_PATTERNS.some(pattern => {
    if (pattern instanceof RegExp) {
      return pattern.test(fileName) || pattern.test(relativePath);
    }
    return fileName.includes(pattern) || relativePath.includes(pattern);
  });
}

function analyzeFileContent(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(process.cwd(), filePath);

    // Check if file is empty or nearly empty
    if (content.trim().length < 50) {
      return { isEmpty: true, reason: 'File is empty or nearly empty' };
    }

    // Check for specific patterns that indicate the file might be important
    const importantPatterns = [
      // API routes and handlers
      /export\s+(default\s+)?async\s+function\s+(GET|POST|PUT|DELETE|PATCH)/,
      /export\s+\{[^}]*\}\s+from/,

      // React components
      /export\s+(default\s+)?function\s+\w+.*\{/,
      /export\s+default\s+function/,
      /const\s+\w+\s*=.*=>\s*\{/,

      // Type definitions
      /export\s+(interface|type|enum)/,
      /declare\s+(module|global|namespace)/,

      // Configuration exports
      /export\s+(default\s+)?(const|let|var)/,

      // Next.js specific exports
      /export\s+(const\s+)?(metadata|generateMetadata|generateStaticParams)/,

      // Utility functions
      /export\s+(const\s+|function\s+)\w+/
    ];

    const hasImportantContent = importantPatterns.some(pattern => pattern.test(content));

    if (!hasImportantContent) {
      return { isEmpty: false, reason: 'No significant exports or functionality detected' };
    }

    return { isEmpty: false, hasContent: true };
  } catch (error) {
    return { isEmpty: false, reason: 'Could not analyze file content' };
  }
}

function findUnusedFiles() {
  console.log('🔍 Scanning for TypeScript and JavaScript files...\n');

  const allFiles = getAllFiles(process.cwd());
  const importedFiles = new Set();
  const entryPoints = new Set();
  const preservedFiles = new Set();
  const emptyFiles = [];
  const suspiciousFiles = [];

  console.log(`Found ${allFiles.length} code files to analyze.\n`);

  // First pass: collect all imports and identify entry points
  allFiles.forEach(filePath => {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const imports = extractImports(content, filePath);

      imports.forEach(importPath => {
        importedFiles.add(path.normalize(importPath));
      });

      if (isEntryPoint(filePath)) {
        entryPoints.add(path.normalize(filePath));
      }

      if (shouldPreserveFile(filePath)) {
        preservedFiles.add(path.normalize(filePath));
      }

      // Analyze file content
      const analysis = analyzeFileContent(filePath);
      if (analysis.isEmpty) {
        emptyFiles.push({ path: filePath, reason: analysis.reason });
      }
    } catch (_error) {
      console.warn(`⚠️  Could not read file: ${filePath}`);
    }
  });

  // Find potentially unused files (not imported, not entry points, not preserved)
  const potentiallyUnusedFiles = allFiles.filter(filePath => {
    const normalizedPath = path.normalize(filePath);
    return !importedFiles.has(normalizedPath) &&
           !entryPoints.has(normalizedPath) &&
           !preservedFiles.has(normalizedPath);
  });

  // Further analyze potentially unused files
  potentiallyUnusedFiles.forEach(filePath => {
    const analysis = analyzeFileContent(filePath);
    if (!analysis.isEmpty && !analysis.hasContent) {
      suspiciousFiles.push({ path: filePath, reason: analysis.reason });
    }
  });

  // Final list of files that are likely safe to delete
  const safeToDeleteFiles = potentiallyUnusedFiles.filter(filePath => {
    const analysis = analyzeFileContent(filePath);
    return analysis.isEmpty || !analysis.hasContent;
  });
  
  // Display results
  console.log('📊 Analysis Results:\n');
  console.log(`Total files analyzed: ${allFiles.length}`);
  console.log(`Entry points identified: ${entryPoints.size}`);
  console.log(`Files with imports: ${importedFiles.size}`);
  console.log(`Preserved files (important): ${preservedFiles.size}`);
  console.log(`Empty/nearly empty files: ${emptyFiles.length}`);
  console.log(`Files likely safe to delete: ${safeToDeleteFiles.length}\n`);

  // Show empty files first (highest confidence for deletion)
  if (emptyFiles.length > 0) {
    console.log('🗑️  Empty or nearly empty files (SAFE TO DELETE):\n');
    emptyFiles.forEach(({ path: filePath, reason }) => {
      const relativePath = path.relative(process.cwd(), filePath);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      console.log(`   ${relativePath} (${sizeKB} KB) - ${reason}`);
    });
    console.log('');
  }

  // Show files that appear to have no significant content
  if (suspiciousFiles.length > 0) {
    console.log('⚠️  Files with no significant exports/functionality (REVIEW BEFORE DELETING):\n');
    suspiciousFiles.forEach(({ path: filePath, reason }) => {
      const relativePath = path.relative(process.cwd(), filePath);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      console.log(`   ${relativePath} (${sizeKB} KB) - ${reason}`);
    });
    console.log('');
  }

  // Show remaining potentially unused files (lower confidence)
  const remainingUnused = potentiallyUnusedFiles.filter(filePath => {
    const analysis = analyzeFileContent(filePath);
    return !analysis.isEmpty && analysis.hasContent;
  });

  if (remainingUnused.length > 0) {
    console.log('🤔 Files not imported but contain functionality (MANUAL REVIEW REQUIRED):\n');
    remainingUnused.forEach(filePath => {
      const relativePath = path.relative(process.cwd(), filePath);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      console.log(`   ${relativePath} (${sizeKB} KB)`);
    });
    console.log('');
  }

  if (safeToDeleteFiles.length > 0) {
    const totalSize = safeToDeleteFiles.reduce((sum, filePath) => {
      return sum + fs.statSync(filePath).size;
    }, 0);

    console.log(`💾 Total size of files safe to delete: ${(totalSize / 1024).toFixed(2)} KB\n`);
  }

  if (safeToDeleteFiles.length === 0 && suspiciousFiles.length === 0) {
    console.log('✅ No obviously redundant files found! Your codebase appears well-organized.\n');
  }
  
  if (entryPoints.size > 0) {
    console.log('🚪 Entry points identified (excluded from unused list):\n');
    Array.from(entryPoints).forEach(filePath => {
      const relativePath = path.relative(process.cwd(), filePath);
      console.log(`   ${relativePath}`);
    });
    console.log('');
  }
}

// Run the analysis
findUnusedFiles();
